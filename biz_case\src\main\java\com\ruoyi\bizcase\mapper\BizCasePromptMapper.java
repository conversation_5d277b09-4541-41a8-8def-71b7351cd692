package com.ruoyi.bizcase.mapper;

import java.util.List;
import com.ruoyi.bizcase.domain.BizCasePrompt;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-04
 */
public interface BizCasePromptMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param promptId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public BizCasePrompt selectBizCasePromptByPromptId(Long promptId);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param bizCasePrompt 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<BizCasePrompt> selectBizCasePromptList(BizCasePrompt bizCasePrompt);

    /**
     * 新增【请填写功能名称】
     * 
     * @param bizCasePrompt 【请填写功能名称】
     * @return 结果
     */
    public int insertBizCasePrompt(BizCasePrompt bizCasePrompt);

    /**
     * 修改【请填写功能名称】
     * 
     * @param bizCasePrompt 【请填写功能名称】
     * @return 结果
     */
    public int updateBizCasePrompt(BizCasePrompt bizCasePrompt);

    /**
     * 删除【请填写功能名称】
     * 
     * @param promptId 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteBizCasePromptByPromptId(Long promptId);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param promptIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBizCasePromptByPromptIds(Long[] promptIds);
}
