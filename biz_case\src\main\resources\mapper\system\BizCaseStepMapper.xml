<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bizcase.mapper.BizCaseStepMapper">
    
    <resultMap type="BizCaseStep" id="BizCaseStepResult">
        <result property="stepId"    column="step_id"    />
        <result property="caseId"    column="case_id"    />
        <result property="stepName"    column="step_name"    />
        <result property="stepOrder"    column="step_order"    />
    </resultMap>

    <sql id="selectBizCaseStepVo">
        select step_id, case_id, step_name, step_order from biz_case_step
    </sql>

    <select id="selectBizCaseStepList" parameterType="BizCaseStep" resultMap="BizCaseStepResult">
        <include refid="selectBizCaseStepVo"/>
        <where>  
            <if test="caseId != null "> and case_id = #{caseId}</if>
            <if test="stepName != null  and stepName != ''"> and step_name like concat('%', #{stepName}, '%')</if>
            <if test="stepOrder != null "> and step_order = #{stepOrder}</if>
        </where>
    </select>
    
    <select id="selectBizCaseStepByStepId" parameterType="Long" resultMap="BizCaseStepResult">
        <include refid="selectBizCaseStepVo"/>
        where step_id = #{stepId}
    </select>

    <insert id="insertBizCaseStep" parameterType="BizCaseStep">
        insert into biz_case_step
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stepId != null">step_id,</if>
            <if test="caseId != null">case_id,</if>
            <if test="stepName != null">step_name,</if>
            <if test="stepOrder != null">step_order,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stepId != null">#{stepId},</if>
            <if test="caseId != null">#{caseId},</if>
            <if test="stepName != null">#{stepName},</if>
            <if test="stepOrder != null">#{stepOrder},</if>
         </trim>
    </insert>

    <update id="updateBizCaseStep" parameterType="BizCaseStep">
        update biz_case_step
        <trim prefix="SET" suffixOverrides=",">
            <if test="caseId != null">case_id = #{caseId},</if>
            <if test="stepName != null">step_name = #{stepName},</if>
            <if test="stepOrder != null">step_order = #{stepOrder},</if>
        </trim>
        where step_id = #{stepId}
    </update>

    <delete id="deleteBizCaseStepByStepId" parameterType="Long">
        delete from biz_case_step where step_id = #{stepId}
    </delete>

    <delete id="deleteBizCaseStepByStepIds" parameterType="String">
        delete from biz_case_step where step_id in 
        <foreach item="stepId" collection="array" open="(" separator="," close=")">
            #{stepId}
        </foreach>
    </delete>
</mapper>