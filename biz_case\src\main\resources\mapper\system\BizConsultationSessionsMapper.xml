<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bizcase.mapper.BizConsultationSessionsMapper">
    
    <resultMap type="BizConsultationSessions" id="BizConsultationSessionsResult">
        <result property="sessionId"    column="session_id"    />
        <result property="userId"    column="user_id"    />
        <result property="caseId"    column="case_id"    />
        <result property="caseTitle"    column="case_title"    />
        <result property="patientName"    column="patient_name"    />
        <result property="totalDuration"    column="total_duration"    />
        <result property="messageCount"    column="message_count"    />
        <result property="status"    column="status"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="createdTime"    column="created_time"    />
        <result property="updatedTime"    column="updated_time"    />
    </resultMap>

    <sql id="selectBizConsultationSessionsVo">
        select session_id, user_id, case_id, case_title, patient_name, total_duration, message_count, status, start_time, end_time, created_time, updated_time from biz_consultation_sessions
    </sql>

    <select id="selectBizConsultationSessionsList" parameterType="BizConsultationSessions" resultMap="BizConsultationSessionsResult">
        <include refid="selectBizConsultationSessionsVo"/>
        <where>  
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="caseId != null  and caseId != ''"> and case_id = #{caseId}</if>
            <if test="caseTitle != null  and caseTitle != ''"> and case_title = #{caseTitle}</if>
            <if test="patientName != null  and patientName != ''"> and patient_name like concat('%', #{patientName}, '%')</if>
            <if test="totalDuration != null  and totalDuration != ''"> and total_duration = #{totalDuration}</if>
            <if test="messageCount != null  and messageCount != ''"> and message_count = #{messageCount}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="createdTime != null "> and created_time = #{createdTime}</if>
            <if test="updatedTime != null "> and updated_time = #{updatedTime}</if>
        </where>
    </select>
    
    <select id="selectBizConsultationSessionsBySessionId" parameterType="String" resultMap="BizConsultationSessionsResult">
        <include refid="selectBizConsultationSessionsVo"/>
        where session_id = #{sessionId}
    </select>

    <insert id="insertBizConsultationSessions" parameterType="BizConsultationSessions" useGeneratedKeys="true" keyProperty="sessionId">
        insert into biz_consultation_sessions
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="caseId != null and caseId != ''">case_id,</if>
            <if test="caseTitle != null and caseTitle != ''">case_title,</if>
            <if test="patientName != null and patientName != ''">patient_name,</if>
            <if test="totalDuration != null">total_duration,</if>
            <if test="messageCount != null">message_count,</if>
            <if test="status != null">status,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="caseId != null and caseId != ''">#{caseId},</if>
            <if test="caseTitle != null and caseTitle != ''">#{caseTitle},</if>
            <if test="patientName != null and patientName != ''">#{patientName},</if>
            <if test="totalDuration != null">#{totalDuration},</if>
            <if test="messageCount != null">#{messageCount},</if>
            <if test="status != null">#{status},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
         </trim>
    </insert>

    <update id="updateBizConsultationSessions" parameterType="BizConsultationSessions">
        update biz_consultation_sessions
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="caseId != null and caseId != ''">case_id = #{caseId},</if>
            <if test="caseTitle != null and caseTitle != ''">case_title = #{caseTitle},</if>
            <if test="patientName != null and patientName != ''">patient_name = #{patientName},</if>
            <if test="totalDuration != null">total_duration = #{totalDuration},</if>
            <if test="messageCount != null">message_count = #{messageCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
        </trim>
        where session_id = #{sessionId}
    </update>

    <delete id="deleteBizConsultationSessionsBySessionId" parameterType="String">
        delete from biz_consultation_sessions where session_id = #{sessionId}
    </delete>

    <delete id="deleteBizConsultationSessionsBySessionIds" parameterType="String">
        delete from biz_consultation_sessions where session_id in 
        <foreach item="sessionId" collection="array" open="(" separator="," close=")">
            #{sessionId}
        </foreach>
    </delete>
</mapper>