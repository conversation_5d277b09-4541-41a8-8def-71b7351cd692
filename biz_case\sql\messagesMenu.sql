-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('问诊对话记录详情', '3', '1', 'messages', 'system/messages/index', 1, 0, 'C', '0', '0', 'system:messages:list', '#', 'admin', sysdate(), '', null, '问诊对话记录详情菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('问诊对话记录详情查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'system:messages:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('问诊对话记录详情新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'system:messages:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('问诊对话记录详情修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'system:messages:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('问诊对话记录详情删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'system:messages:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('问诊对话记录详情导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'system:messages:export',       '#', 'admin', sysdate(), '', null, '');