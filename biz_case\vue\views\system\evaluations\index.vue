<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户 id" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户 id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="病例 id" prop="caseId">
        <el-input
          v-model="queryParams.caseId"
          placeholder="请输入病例 id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="${comment}" prop="stepId">
        <el-input
          v-model="queryParams.stepId"
          placeholder="请输入${comment}"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="整体得分" prop="totleScore">
        <el-input
          v-model="queryParams.totleScore"
          placeholder="请输入整体得分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="整体满分" prop="totleMaxScore">
        <el-input
          v-model="queryParams.totleMaxScore"
          placeholder="请输入整体满分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模式" prop="evalMode">
        <el-input
          v-model="queryParams.evalMode"
          placeholder="请输入模式"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:evaluations:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:evaluations:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:evaluations:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:evaluations:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="evaluationsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="${comment}" align="center" prop="evaluationId" />
      <el-table-column label="用户 id" align="center" prop="userId" />
      <el-table-column label="病例 id" align="center" prop="caseId" />
      <el-table-column label="${comment}" align="center" prop="stepId" />
      <el-table-column label="整体得分" align="center" prop="totleScore" />
      <el-table-column label="整体满分" align="center" prop="totleMaxScore" />
      <el-table-column label="模式" align="center" prop="evalMode" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:evaluations:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:evaluations:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改学生病例得分总对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户 id" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户 id" />
        </el-form-item>
        <el-form-item label="病例 id" prop="caseId">
          <el-input v-model="form.caseId" placeholder="请输入病例 id" />
        </el-form-item>
        <el-form-item label="${comment}" prop="stepId">
          <el-input v-model="form.stepId" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="整体得分" prop="totleScore">
          <el-input v-model="form.totleScore" placeholder="请输入整体得分" />
        </el-form-item>
        <el-form-item label="整体满分" prop="totleMaxScore">
          <el-input v-model="form.totleMaxScore" placeholder="请输入整体满分" />
        </el-form-item>
        <el-form-item label="模式" prop="evalMode">
          <el-input v-model="form.evalMode" placeholder="请输入模式" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listEvaluations, getEvaluations, delEvaluations, addEvaluations, updateEvaluations } from "@/api/system/evaluations"

export default {
  name: "Evaluations",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 学生病例得分总表格数据
      evaluationsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        caseId: null,
        stepId: null,
        totleScore: null,
        totleMaxScore: null,
        evalMode: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询学生病例得分总列表 */
    getList() {
      this.loading = true
      listEvaluations(this.queryParams).then(response => {
        this.evaluationsList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        evaluationId: null,
        userId: null,
        caseId: null,
        stepId: null,
        totleScore: null,
        totleMaxScore: null,
        evalMode: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.evaluationId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加学生病例得分总"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const evaluationId = row.evaluationId || this.ids
      getEvaluations(evaluationId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改学生病例得分总"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.evaluationId != null) {
            updateEvaluations(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addEvaluations(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const evaluationIds = row.evaluationId || this.ids
      this.$modal.confirm('是否确认删除学生病例得分总编号为"' + evaluationIds + '"的数据项？').then(function() {
        return delEvaluations(evaluationIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/evaluations/export', {
        ...this.queryParams
      }, `evaluations_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
