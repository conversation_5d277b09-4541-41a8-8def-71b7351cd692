Stack trace:
Frame         Function      Args
0007FFFFA4F0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF93F0) msys-2.0.dll+0x1FE8E
0007FFFFA4F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA7C8) msys-2.0.dll+0x67F9
0007FFFFA4F0  000210046832 (000210286019, 0007FFFFA3A8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA4F0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA4F0  000210068E24 (0007FFFFA500, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA7D0  00021006A225 (0007FFFFA500, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9B31D0000 ntdll.dll
7FF9B1BC0000 KERNEL32.DLL
7FF9B0750000 KERNELBASE.dll
7FF9B1390000 USER32.dll
7FF9B0450000 win32u.dll
000210040000 msys-2.0.dll
7FF9B30B0000 GDI32.dll
7FF9B0480000 gdi32full.dll
7FF9B02F0000 msvcp_win.dll
7FF9B0630000 ucrtbase.dll
7FF9B2F80000 advapi32.dll
7FF9B16A0000 msvcrt.dll
7FF9B30E0000 sechost.dll
7FF9B02C0000 bcrypt.dll
7FF9B1760000 RPCRT4.dll
7FF9AFA60000 CRYPTBASE.DLL
7FF9B05B0000 bcryptPrimitives.dll
7FF9B1880000 IMM32.DLL
